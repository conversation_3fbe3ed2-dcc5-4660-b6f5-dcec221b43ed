package cpv_service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	anycache "code.byted.org/webcast/libs_anycache"
	"context"
	"fmt"
	"time"
)

var GetAllLeafCategoryCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v.%v", env.PSM(), "motor.fwe_ecom.category.GetAllLeafCategory")
	logicExpireTime := 1 * time.Minute
	physicalExpireTime := 2 * time.Minute
	GetAllLeafCategoryCache = anycache.NewDefaultBytesCache().
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return fmt.Sprintf("getAllLeafCategory:id:%d", categoryID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				leafMap, err := getAllLeafCategory(ctx, categoryIDs)
				if err != nil {
					return nil, err
				}
				return leafMap, nil
			},
		)
}

func getAllLeafCategory(ctx context.Context, categoryIdList []int64) (map[int64][]*do.Category, error) {
	var (
		maxLevel             = 3
		leafMap              = make(map[int64][]*do.Category)
		newParentCategoryIds = make([]int64, 0)
		readStrategy         = category.ReadStrategy_CacheDefault
		parentToChildren     = make(map[int64][]*do.Category)
	)
	// 1. 查询当前Category
	categoryList, err := CategoryService.mGetCategoryByID(ctx, categoryIdList, &readStrategy)
	if err != nil {
		logs.CtxError(ctx, "[MGetAllLeafCategory] getCategoryListByID err: %+v", err)
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
	}
	for _, item := range categoryList {
		if item == nil {
			continue
		}
		if item.ParentID != 0 {
			parentToChildren[item.ParentID] = append(parentToChildren[item.ParentID], item)
		}
		if item.IsLeaf != 1 && item.ID != 0 {
			newParentCategoryIds = append(newParentCategoryIds, item.ID)
		}
	}

	for i := 0; i < maxLevel && len(newParentCategoryIds) > 0; i++ {
		categoryList, err := CategoryService.queryByParentIDs(ctx, newParentCategoryIds, &readStrategy)
		if err != nil {
			logs.CtxError(ctx, "[MGetAllLeafCategory] rpc err: %+v", err)
			return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
		}
		newParentCategoryIds = make([]int64, 0)
		for _, cat := range categoryList {
			if cat.ID != 0 && cat.IsLeaf != 1 {
				newParentCategoryIds = append(newParentCategoryIds, cat.ID)
			}
			if cat.ParentID != 0 {
				parentToChildren[cat.ParentID] = append(parentToChildren[cat.ParentID], cat)
			}
		}
	}
	for _, id := range categoryIdList {
		leafs := findLeafs(id, parentToChildren)
		leafMap[id] = gslice.UniqBy(leafs, func(c *do.Category) int64 { return c.ID })
	}
	return leafMap, nil
}

func recursiveFindLeafs(parentID int64, parentToChildrenMap map[int64][]*do.Category) []*do.Category {
	var result []*do.Category
	children, ok := parentToChildrenMap[parentID]
	if !ok {
		return result
	}

	for _, child := range children {
		if child.IsLeaf == 1 {
			result = append(result, child)
		} else {
			result = append(result, findLeafs(child.ID, parentToChildrenMap)...)
		}
	}
	return result
}

func findLeafs(parentID int64, parentToChildrenMap map[int64][]*do.Category) []*do.Category {
	var result []*do.Category
	queue := []int64{parentID}

	// 使用一个 map 来记录已处理过的节点，防止因数据问题产生无限循环
	visited := make(map[int64]bool)

	for len(queue) > 0 {
		// 从队列头部取出一个ID
		currentID := queue[0]
		queue = queue[1:]

		if visited[currentID] {
			continue
		}
		visited[currentID] = true

		// 查找当前ID的所有子节点
		children, ok := parentToChildrenMap[currentID]
		if !ok {
			continue
		}

		for _, child := range children {
			if child.IsLeaf == 1 {
				// 如果是叶子节点，加入结果列表
				result = append(result, child)
			} else {
				// 如果不是叶子节点，将其ID加入队列等待后续处理
				queue = append(queue, child.ID)
			}
		}
	}
	return result
}
