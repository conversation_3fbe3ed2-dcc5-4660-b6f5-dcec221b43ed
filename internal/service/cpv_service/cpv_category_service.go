package cpv_service

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/lang/v2/mathx"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/gvalue"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"gorm.io/gen/field"
)

type categoryService struct{}

var CategoryService = new(categoryService)

func (s *categoryService) Query(ctx context.Context, req *category.QueryCategoryReq) (rsp *category.QueryCategoryRsp, bizErr *errdef.BizErr) {
	rsp = &category.QueryCategoryRsp{}
	queryParam := &dal.CategoryQueryParam{
		CategoryIDList:       req.CategoryIdList,
		BizLineList:          req.BizLineList,
		ParentCategoryIdList: req.ParentCategoryIdList,
		NameKeyword:          req.NameKeyword,
		Level:                req.Level,
		Limit:                mathx.MaxInteger(int(req.Limit), 1),
		Offset:               int(conv.PtrToValueOrDefault(req.Offset, 0)),
		NeedTotal:            true,
	}
	var doList []*do.Category
	doList, rsp.Total, bizErr = dal.Category.Query(ctx, nil, queryParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	for _, v := range doList {
		w := converter.NewPO2DO().TranCategory(v)
		if w == nil {
			continue
		}
		rsp.CategoryList = append(rsp.CategoryList, w)
	}
	rsp.HasMore = rsp.Total > int64(queryParam.Limit+queryParam.Offset)
	return
}

func (s *categoryService) GetAllParentIDs(ctx context.Context, categoryIDs []int64) (allIDs []int64, bizErr *errdef.BizErr) {
	var (
		dataList []*do.Category
		selectS  = []field.Expr{mysql.Category.ID, mysql.Category.ParentID}
		thisIDs  = categoryIDs
	)
	// 最多三层查询，这里5是兜底之后会新增
	for i := 0; i < 5; i++ {
		thisIDs = slicex.Filter(slicex.Distinct(thisIDs), gvalue.IsNotZero[int64])
		if len(thisIDs) == 0 {
			break
		}
		allIDs = append(allIDs, thisIDs...)
		queryParam := &dal.CategoryQueryParam{
			SelectColumns:  selectS,
			CategoryIDList: thisIDs,
			Limit:          len(thisIDs),
		}
		dataList, _, bizErr = dal.Category.Query(ctx, nil, queryParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		thisIDs = gslice.Map(dataList, func(x *do.Category) int64 { return x.ParentID })
	}
	return
}

func (s *categoryService) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq) (resp *category.MGetAllLeafCategoryResp, bizErr *errdef.BizErr) {
	resp = category.NewMGetAllLeafCategoryResp()
	var (
		maxLevel             = 3
		leafCategory         []*do.Category
		newParentCategoryIds = make([]int64, 0)
		readStrategy         = req.GetReadStrategy()
	)
	// 1. 查询当前Category
	var categoryList []*do.Category
	categoryList, bizErr = s.mGetCategoryByID(ctx, req.GetCategoryIdList(), &readStrategy)
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetAllLeafCategory] mGetCategoryByID err: %+v", bizErr)
		return
	}
	for _, item := range categoryList {
		if item != nil && item.IsLeaf == 1 {
			leafCategory = append(leafCategory, item)
		} else {
			if item != nil && item.ID != 0 {
				newParentCategoryIds = append(newParentCategoryIds, item.ID)
			}
		}
	}

	for i := 0; i < maxLevel && len(newParentCategoryIds) > 0; i++ {
		var categoryList []*do.Category
		categoryList, bizErr = s.queryByParentIDs(ctx, newParentCategoryIds, &readStrategy)
		if bizErr != nil {
			logs.CtxError(ctx, "[MGetAllLeafCategory] queryByParentIDs err: %+v", bizErr)
			return
		}
		logs.CtxInfo(ctx, "[MGetAllLeafCategory] categoryList: %+v", categoryList)
		newParentCategoryIds = make([]int64, 0)
		for _, cat := range categoryList {
			if cat.IsLeaf == 1 {
				leafCategory = append(leafCategory, cat)
			} else if cat.ID != 0 {
				newParentCategoryIds = append(newParentCategoryIds, cat.ID)
			}
		}

		if len(newParentCategoryIds) == 0 {
			break
		}
	}
	leafCategoryList := gslice.Map(leafCategory, func(x *do.Category) *category.CategoryBase { return converter.NewPO2DO().TranCategory(x) })
	resp.SetLeafCategoryList(leafCategoryList)
	return resp, nil
}
func (s *categoryService) mGetCategoryByID(ctx context.Context, catIDs []int64, readStrategy *category.ReadStrategy) (categoryList []*do.Category, bizErr *errdef.BizErr) {
	if readStrategy != nil && *readStrategy == category.ReadStrategy_CacheDefault {
		// 从缓存批量获取数据
		_, err := MGetCategoryCache.MGet(ctx, catIDs, &categoryList)
		if err != nil {
			logs.CtxWarn(ctx, "[CategoryService] getCategoryListByID from cache failed, err: %v", err)
			bizErr = biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
			return
		}
	} else {
		// 从数据库中获取数据
		var err error
		categoryList, err = dal.MGetCategoryWithIsTest(ctx, catIDs, nil, nil)
		if err != nil {
			logs.CtxError(ctx, "[CategoryService] getCategoryListByID from db failed, err: %v", err)
			bizErr = biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
			return
		}
	}
	categoryList = gslice.Filter(categoryList, func(category *do.Category) bool { return category != nil })
	return
}

func (s *categoryService) queryByParentIDs(ctx context.Context, parentCategoryIDs []int64, readStrategy *category.ReadStrategy) (categoryList []*do.Category, bizErr *errdef.BizErr) {
	if readStrategy != nil && *readStrategy == category.ReadStrategy_CacheDefault {
		result := new([][]*do.Category)
		_, err := QueryCategoryCache.MGet(ctx, parentCategoryIDs, result)
		if err != nil {
			logs.CtxError(ctx, "[GetAllLeafCategory] queryByParentIDs from cache failed, err: %+v", err)
			bizErr = biz_err.DBErr.WithErr(err).WithMessage("根据父类目ID获取类目失败")
			return
		}
		categoryList = gslice.Uniq(gslice.Flatten(*result))
	} else {
		queryParam := &dal.CategoryQueryParam{
			ParentCategoryIdList: parentCategoryIDs,
			Limit:                500,
		}
		logs.CtxInfo(ctx, "[GetAllLeafCategory] queryParam: %+v", queryParam)
		categoryList, _, bizErr = dal.Category.Query(ctx, nil, queryParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[GetAllLeafCategory] queryByParentIDs from db failed, err=%s", bizErr.Error())
			return
		}
	}
	categoryList = gslice.Filter(categoryList, func(category *do.Category) bool { return category != nil })
	return
}
