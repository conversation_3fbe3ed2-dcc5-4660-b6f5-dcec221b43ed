package cpv_service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"context"
	"fmt"
	"time"
)

var MGetCategoryCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v.%v", env.PSM(), "motor.fwe_ecom.category.MGetCategory")
	logicExpireTime := 1 * time.Minute
	physicalExpireTime := 2 * time.Minute
	MGetCategoryCache = anycache.NewDefaultBytesCache().
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return fmt.Sprintf("category:id:%d", categoryID)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				category := item.(*do.Category)
				return fmt.Sprintf("category:id:%d", category.ID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				categories, err := dal.MGetCategoryWithIsTest(ctx, categoryIDs, nil, nil)
				if err != nil {
					return nil, err
				}
				return categories, nil
			},
		)
}
