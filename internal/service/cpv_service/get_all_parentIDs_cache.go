package cpv_service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	anycache "code.byted.org/webcast/libs_anycache"
	"context"
	"fmt"
	"gorm.io/gen/field"
	"time"
)

var GetAllParentIDsCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v.%v", env.PSM(), "motor.fwe_ecom.category.GetAllParentIDs")
	logicExpireTime := 1 * time.Minute
	physicalExpireTime := 2 * time.Minute
	GetAllParentIDsCache = anycache.NewDefaultBytesCache().
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return fmt.Sprintf("childCategory:id:%d", categoryID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				// 批量从DB获取父链路
				parentMap, err := mGetAllParentIDs(ctx, categoryIDs)
				if err != nil {
					return nil, err
				}
				return parentMap, nil
			},
		)
}

func mGetAllParentIDs(ctx context.Context, categoryIDs []int64) (map[string][]int64, *errdef.BizErr) {
	var (
		maxLevel = 3
		result   = map[string][]int64{}
	)
	for _, id := range categoryIDs {
		result[fmt.Sprintf("childCategory:id:%d", id)] = []int64{id} // 每个ID的父链路都包含自己
	}

	// childID -> parentID 的映射
	childToParent := make(map[int64]int64)
	currentIDs := gslice.Uniq(categoryIDs)

	// 通过循环批量获取所有层级的父ID，直到最顶层
	for i := 0; i < maxLevel && len(currentIDs) > 0; i++ {
		queryParam := &dal.CategoryQueryParam{
			SelectColumns:  []field.Expr{mysql.Category.ID, mysql.Category.ParentID},
			CategoryIDList: currentIDs,
		}
		dataList, _, bizErr := dal.Category.Query(ctx, nil, queryParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[MGetAllParentIDs] query failed: %v", bizErr)
			return nil, bizErr
		}

		nextIDs := make([]int64, 0)
		for _, cat := range dataList {
			if cat.ID != 0 && cat.ParentID != 0 {
				childToParent[cat.ID] = cat.ParentID
				nextIDs = append(nextIDs, cat.ParentID)
			}
		}
		currentIDs = gslice.Uniq(nextIDs)
	}

	// 基于已构建的 childToParent 映射，为每个初始ID回溯出完整的父链路
	for _, catID := range categoryIDs {
		curr := catID
		for {
			parentID, ok := childToParent[curr]
			if !ok || parentID == 0 {
				break
			}
			result[fmt.Sprintf("childCategory:id:%d", catID)] = append(result[fmt.Sprintf("childCategory:id:%d", catID)], parentID)
			curr = parentID
		}
	}

	return result, nil
}
