package cpv_service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"context"
	"fmt"
	"time"
)

var TenantPropertyRelCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v.%v", env.PSM(), "motor.fwe_ecom.category.TenantPropRel")
	logicExpireTime := 1 * time.Minute
	physicalExpireTime := 2 * time.Minute

	TenantPropertyRelCache = anycache.NewDefaultBytesCache().
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				tenantType := item.(int64)
				return fmt.Sprintf("tenant_prop_rel:tenant_type:%d", tenantType)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				tenantTypes := missedItems.([]int64)
				queryParam := &dal.TenantRelQueryParam{
					TenantTypeS: tenantTypes,
					Limit:       500,
				}
				relations, bizErr := dal.TenantPropRel.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				// 手动将返回的 slice 转换为 map
				resultMap := make(map[string][]*do.ProductTenantPropertyRel)
				for _, relation := range relations {
					key := fmt.Sprintf("tenant_prop_rel:tenant_type:%d", relation.TenantType)
					resultMap[key] = append(resultMap[key], relation)
				}
				return resultMap, nil
			},
		)
}
