package cpv_service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"context"
	"fmt"
	"time"
)

var QueryPropertyCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v.%v", env.PSM(), "motor.fwe_ecom.category.Property")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	QueryPropertyCache = anycache.NewDefaultBytesCache().
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				propertyKey := item.(string)
				return fmt.Sprintf("property:key:%s", propertyKey)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				property := item.(*do.ProductProperty)
				return fmt.Sprintf("property:key:%s", property.PropertyKey)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				propertyKeys := missedItems.([]string)
				queryParam := &dal.PropQueryParam{
					PropertyKeyList: propertyKeys,
					Limit:           len(propertyKeys),
				}
				properties, _, bizErr := dal.ProductProperty.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				return properties, nil
			},
		)
}
